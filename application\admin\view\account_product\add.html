<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Price')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-price" data-rule="required;decimal" class="form-control" step="0.01" name="row[price]" type="number" value="">
        </div>
    </div>
    
    <div class="form-group" id="product-url-group" style="display:none;">
        <label class="control-label col-xs-12 col-sm-2">{:__('Product_url')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-product_url" data-rule="" class="form-control" name="row[product_url]" type="url" value="">
        </div>
    </div>
    
    <div class="form-group" id="qrcode-group" style="display:none;">
        <label for="c-qrcode" class="control-label col-xs-12 col-sm-2">二维码:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-qrcode" data-rule="" class="form-control" size="50" name="row[qrcode]" type="text" value="">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-qrcode" class="btn btn-danger faupload" data-input-id="c-qrcode" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp" data-multiple="false" data-preview-id="p-qrcode"><i class="fa fa-upload"></i> 上传</button></span>
                </div>
                <span class="msg-box n-right" for="c-qrcode"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-qrcode"></ul>
        </div>
    </div>
    
    <div class="form-group" id="sharl-code-group" style="display:none;">
        <label class="control-label col-xs-12 col-sm-2">{:__('口令')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-sharl_code" class="form-control" name="row[sharl_code]" type="text" value="">
        </div>
    </div>

<script>
// 将账号通道信息传递给JS
window.Config = window.Config || {};
</script>
    
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>









