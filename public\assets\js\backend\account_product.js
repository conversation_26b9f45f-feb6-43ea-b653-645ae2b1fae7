define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'account_product/index' + location.search,
                    add_url: 'account_product/add' + location.search,
                    edit_url: 'account_product/edit',
                    del_url: 'account_product/del',
                    multi_url: 'account_product/multi',
                    import_url: 'account_product/import',
                    table: 'account_product',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                pageSize: 50,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'price', title: __('Price'), operate: 'BETWEEN', formatter: function(value, row, index) {
                            return '¥' + parseFloat(value).toFixed(2);
                        }},
                        {field: 'product_url', title: __('Product_url'), operate: 'LIKE', formatter: function(value, row, index) {
                            return value ? '<a href="' + value + '" target="_blank">' + value + '</a>' : '';
                        }},
                        {field: 'qrcode', title: __('二维码'), operate: 'LIKE', formatter: function(value, row, index) {
                            return value ? '<img src="' + value + '" style="max-width:50px;max-height:50px;" onclick="Fast.api.open(\'' + value + '\', \'查看二维码\', {area: [\'auto\', \'auto\']})">' : '';
                        }},
                        {field: 'sharl_code', title: __('口令')},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'updatetime', title: __('Updatetime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);

            // 批量添加按钮事件
            $(".btn-batchadd").on('click', function () {
                var account_id = Fast.api.query('account_id');
                Fast.api.open('account_product/batchadd?account_id=' + account_id, '批量添加商品', {
                    area: ['800px', '600px'],
                    callback: function(data) {
                        // 批量添加成功后刷新当前页面的表格
                        $("#table").bootstrapTable('refresh');
                    }
                });
            });
        },
        add: function () {
            // 延迟执行，确保页面元素已加载
            setTimeout(function() {
                // 从URL参数获取account_id
                var account_id = Fast.api.query('account_id');
                console.log('获取到的account_id:', account_id);
                
                if (account_id) {
                    // 通过Ajax获取账号信息
                    Fast.api.ajax({
                        url: 'account_product/get_account_info',
                        data: {account_id: account_id}
                    }, function(data) {
                        console.log('获取到的账号信息:', data);
                        var accountChannel = data.channel;
                        
                        // 根据账号通道模式显示对应字段
                        function showFieldByChannel(channel) {
                            console.log('显示字段，通道:', channel);
                            // 隐藏所有字段组
                            $('#product-url-group, #qrcode-group, #sharl-code-group').hide();
                            
                            // 清空所有字段的验证规则
                            $('#c-product_url, #c-qrcode, #c-sharl_code').attr('data-rule', '');
                            
                            // 根据通道模式显示对应字段
                            switch(channel) {
                                case '9008': // 口令模式
                                    $('#sharl-code-group').show();
                                    $('#c-sharl_code').attr('data-rule', 'required');
                                    console.log('显示口令字段');
                                    break;
                                case '9011': // 链接模式  
                                    $('#product-url-group').show();
                                    $('#c-product_url').attr('data-rule', 'required;url');
                                    console.log('显示链接字段');
                                    break;
                                case '9012': // 二维码模式
                                    $('#qrcode-group').show();
                                    $('#c-qrcode').attr('data-rule', 'required');
                                    console.log('显示二维码字段');
                                    break;
                                default:
                                    console.log('未知通道模式:', channel);
                            }
                        }
                        
                        if (accountChannel) {
                            showFieldByChannel(accountChannel);
                        }
                    });
                }
            }, 100);
            
            Controller.api.bindevent();
        },
        edit: function () {
            // 延迟执行，确保页面元素已加载
            setTimeout(function() {
                // 获取账号通道信息（从后端传递的变量）
                var accountChannel = Config.accountChannel || '';
                console.log('编辑页面获取到的账号通道:', accountChannel);

                // 根据账号通道模式显示对应字段
                function showFieldByChannel(channel) {
                    console.log('编辑页面显示字段，通道:', channel);
                    // 隐藏所有字段组
                    $('#product-url-group, #qrcode-group, #sharl-code-group').hide();

                    // 清空所有字段的验证规则
                    $('#c-product_url, #c-qrcode, #c-sharl_code').attr('data-rule', '');

                    // 根据通道模式显示对应字段
                    switch(channel) {
                        case '9008': // 口令模式
                            $('#sharl-code-group').show();
                            $('#c-sharl_code').attr('data-rule', 'required');
                            console.log('编辑页面显示口令字段');
                            break;
                        case '9011': // 链接模式
                            $('#product-url-group').show();
                            $('#c-product_url').attr('data-rule', 'required;url');
                            console.log('编辑页面显示链接字段');
                            break;
                        case '9012': // 二维码模式
                            $('#qrcode-group').show();
                            $('#c-qrcode').attr('data-rule', 'required');
                            console.log('编辑页面显示二维码字段');
                            break;
                        default:
                            console.log('编辑页面未知通道模式:', channel);
                    }
                }

                // 初始化时根据账号通道模式显示字段
                if (accountChannel) {
                    showFieldByChannel(accountChannel);
                }
            }, 100);

            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});







